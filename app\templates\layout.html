<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- 使用Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 自定义Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#22c55e',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#0ea5e9'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .transition-card {
                transition: all 0.3s ease;
            }
            .hover-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
        }
    </style>
    <!-- Markdown 样式支持 -->
    <style>
        /* 基本Markdown样式 */
        .prose h1 { font-size: 2em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h2 { font-size: 1.5em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h3 { font-size: 1.25em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h4 { font-size: 1em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h5 { font-size: 0.875em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h6 { font-size: 0.85em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        
        .prose p { margin-bottom: 1em; }
        .prose a { color: #3b82f6; text-decoration: underline; }
        .prose a:hover { color: #2563eb; }
        
        .prose ul { list-style-type: disc; margin-left: 1.5em; margin-bottom: 1em; }
        .prose ol { list-style-type: decimal; margin-left: 1.5em; margin-bottom: 1em; }
        .prose li { margin-bottom: 0.25em; }
        
        .prose blockquote { 
            border-left: 4px solid #d1d5db; 
            padding-left: 1em; 
            margin-left: 0;
            margin-bottom: 1em;
            color: #6b7280;
        }
        
        .prose pre { 
            background-color: #f3f4f6; 
            padding: 1em; 
            border-radius: 0.25em; 
            overflow-x: auto;
            margin-bottom: 1em;
        }
        
        .prose code { 
            background-color: #f3f4f6; 
            padding: 0.2em 0.4em; 
            border-radius: 0.25em; 
            font-family: monospace;
        }
        
        .prose pre code {
            background-color: transparent;
            padding: 0;
        }
        
        .prose img { 
            max-width: 100%; 
            height: auto;
            margin: 1em 0;
        }
        
        .prose hr {
            border: 0;
            border-top: 1px solid #e5e7eb;
            margin: 2em 0;
        }
        
        .prose table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
        }
        
        .prose th, .prose td {
            border: 1px solid #d1d5db;
            padding: 0.5em;
        }
        
        .prose th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
        
        /* 小预览的样式微调 */
        .markdown-preview h1, .markdown-preview h2, .markdown-preview h3 { 
            margin-top: 0.5em; 
            margin-bottom: 0.25em; 
        }

        /* 分类标签栏样式 */
        .category-tag-bar {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
        }

        .category-tag-item {
            transition: all 0.2s ease;
        }

        .category-tag-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 隐藏滚动条 */
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
    </style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
</head>
<body class="bg-gray-50 text-gray-800 min-h-screen flex flex-col pt-24 md:pt-32">
    <!-- 固定顶部导航栏 -->
    <header class="bg-gradient-to-r from-blue-800 to-indigo-900 shadow-md fixed top-0 left-0 right-0 z-50">
        <nav class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <a class="text-white text-xl font-bold flex items-center" href="{{ url_for('index') }}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    卡片管理系统
                </a>
                
                <!-- 搜索框 - 桌面版 -->
                <div class="hidden md:block mx-4 flex-grow max-w-md">
                    <form action="{{ url_for('search') }}" method="GET" class="relative" id="searchForm">
                        <input type="text" name="q" id="searchInput" placeholder="搜索卡片内容..." 
                               class="w-full px-4 py-2 rounded-full bg-indigo-700/60 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-primary"
                               autocomplete="off">
                        <div class="absolute right-0 top-0 h-full flex items-center pr-3">
                            <button type="button" id="clearSearch" class="text-indigo-200 hover:text-white mr-2 hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <button type="submit" class="text-indigo-200 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="hidden md:block">
                    <ul class="flex space-x-6">
                        <li>
                            <a class="text-indigo-200 hover:text-white flex items-center transition-colors" href="{{ url_for('index') }}" title="首页">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a class="text-indigo-200 hover:text-white flex items-center transition-colors" href="{{ url_for('new_card') }}" title="新建卡片">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 移动端菜单按钮 -->
                <button id="menu-toggle" class="md:hidden text-indigo-200 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
            
            <!-- 移动端菜单 -->
            <div id="mobile-menu" class="md:hidden hidden mt-3">
                <!-- 搜索框 - 移动版 -->
                <div class="mb-3">
                    <form action="{{ url_for('search') }}" method="GET" class="relative">
                        <input type="text" name="q" placeholder="搜索卡片内容..." 
                               class="w-full px-4 py-2 rounded-full bg-indigo-700/60 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-primary">
                        <button type="submit" class="absolute right-0 top-0 mr-3 mt-2 text-indigo-200 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </form>
                </div>
                <ul class="flex flex-col space-y-3 py-2">
                    <li>
                        <a class="flex items-center text-indigo-200 hover:text-white transition-colors" href="{{ url_for('index') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            首页
                        </a>
                    </li>
                    <li>
                        <a class="flex items-center text-indigo-200 hover:text-white transition-colors" href="{{ url_for('new_card') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            新建卡片
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 分类和标签快速访问栏 -->
    <div class="category-tag-bar fixed top-16 left-0 right-0 z-40 py-1.5 hidden md:block">
        <div class="container mx-auto px-4">
            <div class="flex flex-col space-y-1.5">
                <!-- 分类栏 -->
                {% if all_categories %}
                <div class="flex items-center">
                    <div class="flex items-center mr-3 text-sm font-medium text-gray-600 min-w-max">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        分类:
                    </div>
                    <div class="flex flex-wrap gap-2 overflow-x-auto scrollbar-hide">
                        {% for category in all_categories[:8] %}
                        <a href="{{ url_for('category', category=category) }}"
                           class="category-tag-item inline-block bg-white border border-indigo-200 text-indigo-700 text-xs px-3 py-1 rounded-full hover:bg-indigo-50 hover:border-indigo-300 transition-colors whitespace-nowrap">
                            {{ category }}
                        </a>
                        {% endfor %}
                        {% if all_categories|length > 8 %}
                        <span class="text-xs text-gray-500 px-2 py-1 whitespace-nowrap">+{{ all_categories|length - 8 }}更多</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 标签栏 -->
                {% if all_tags %}
                <div class="flex items-center">
                    <div class="flex items-center mr-3 text-sm font-medium text-gray-600 min-w-max">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        标签:
                    </div>
                    <div class="flex flex-wrap gap-2 overflow-x-auto scrollbar-hide">
                        {% for tag in all_tags[:10] %}
                        <a href="{{ url_for('tag', tag=tag) }}"
                           class="category-tag-item inline-block bg-white border border-blue-200 text-blue-700 text-xs px-3 py-1 rounded-full hover:bg-blue-50 hover:border-blue-300 transition-colors whitespace-nowrap">
                            {{ tag }}
                        </a>
                        {% endfor %}
                        {% if all_tags|length > 10 %}
                        <span class="text-xs text-gray-500 px-2 py-1 whitespace-nowrap">+{{ all_tags|length - 10 }}更多</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 移动端分类标签栏 -->
    <div class="md:hidden fixed top-16 left-0 right-0 z-40 bg-white border-b border-gray-200">
        <div class="px-4 py-1.5">
            <!-- 分类快速访问 -->
            {% if all_categories %}
            <div class="mb-1.5">
                <div class="flex items-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    <span class="text-xs font-medium text-gray-600">分类</span>
                </div>
                <div class="flex gap-1 overflow-x-auto scrollbar-hide pb-1">
                    {% for category in all_categories[:6] %}
                    <a href="{{ url_for('category', category=category) }}"
                       class="inline-block bg-indigo-50 border border-indigo-200 text-indigo-700 text-xs px-2 py-1 rounded-full whitespace-nowrap">
                        {{ category }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 标签快速访问 -->
            {% if all_tags %}
            <div>
                <div class="flex items-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                    </svg>
                    <span class="text-xs font-medium text-gray-600">标签</span>
                </div>
                <div class="flex gap-1 overflow-x-auto scrollbar-hide pb-1">
                    {% for tag in all_tags[:8] %}
                    <a href="{{ url_for('tag', tag=tag) }}"
                       class="inline-block bg-blue-50 border border-blue-200 text-blue-700 text-xs px-2 py-1 rounded-full whitespace-nowrap">
                        {{ tag }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <main class="container mx-auto px-4 pt-2 pb-6 flex-grow">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% if category == 'success' %}
                        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'danger' or category == 'error' %}
                        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'warning' %}
                        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'info' %}
                        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% else %}
                        <div class="bg-gray-100 border-l-4 border-gray-500 text-gray-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% endif %}
                        <p class="font-medium">{{ message }}</p>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <footer class="bg-gradient-to-r from-blue-800 to-indigo-900 text-gray-300 text-center py-4 mt-auto">
        <p>卡片内容管理系统 &copy; {{ now.year if now else 2025 }}</p>
    </footer>

    <!-- 移动端菜单切换脚本 -->
    <script>
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // 提示消息自动消失功能
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    message.style.transition = 'opacity 0.5s ease';
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.remove();
                    }, 500);
                }, 2000);
            });
        });

        // 搜索功能
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        const clearSearch = document.getElementById('clearSearch');
        let searchTimeout;
        let searchStatus = document.createElement('div');
        searchStatus.className = 'absolute left-0 -bottom-8 text-sm text-blue-600 bg-white px-2 py-1 rounded shadow-sm hidden';
        
        if (searchForm) {
            searchForm.parentNode.style.position = 'relative';
            searchForm.parentNode.appendChild(searchStatus);
        }

        if (searchInput && clearSearch) {
            // 显示/隐藏清除按钮
            searchInput.addEventListener('input', function() {
                clearSearch.classList.toggle('hidden', !this.value);
                
                // 清除之前的延时
                clearTimeout(searchTimeout);
                
                const query = this.value.trim();
                
                // 隐藏状态信息
                searchStatus.classList.add('hidden');
                
                // 如果输入为空，不处理
                if (!query) return;
                
                // 如果字符太少，显示提示但不搜索
                if (query.length < 2) {
                    searchStatus.textContent = '请至少输入2个字符';
                    searchStatus.classList.remove('hidden');
                    return;
                }
                
                // 显示等待提示
                searchStatus.textContent = `将在1秒后搜索: "${query}"`;
                searchStatus.classList.remove('hidden');
                
                // 设置新的延时
                searchTimeout = setTimeout(() => {
                    if (query.length >= 2) {
                        searchForm.submit();
                    }
                }, 1000); // 1000ms 延迟，给用户更多时间考虑和修改
            });

            // 清除搜索内容
            clearSearch.addEventListener('click', function() {
                searchInput.value = '';
                clearSearch.classList.add('hidden');
                searchStatus.classList.add('hidden');
                searchInput.focus();
            });

            // 处理表单提交事件
            searchForm.addEventListener('submit', function(e) {
                const query = searchInput.value.trim();
                
                // 如果搜索词少于2个字符，阻止提交
                if (query.length < 2) {
                    e.preventDefault();
                    searchStatus.textContent = '请至少输入2个字符';
                    searchStatus.classList.remove('hidden');
                    return false;
                }
            });
        }
    </script>
</body>
</html> 