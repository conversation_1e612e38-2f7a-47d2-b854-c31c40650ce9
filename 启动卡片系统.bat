@echo off
chcp 65001 > nul
title 卡片内容管理系统

REM 一定要先启用延迟环境变量扩展
setlocal enabledelayedexpansion

echo ================================================
echo             卡片内容管理系统启动
echo ================================================

REM 检查虚拟环境是否存在
if not exist venv (
    echo 错误: 虚拟环境不存在!
    echo 请先运行 setup.bat 创建虚拟环境并安装依赖
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo 正在激活虚拟环境...
call venv\Scripts\activate.bat
if !errorlevel! neq 0 (
    echo 激活虚拟环境失败!
    echo 请检查venv目录是否完整。
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

REM 检查数据库目录是否存在
if not exist app\database (
    echo 创建数据库目录...
    mkdir app\database
    if !errorlevel! neq 0 (
        echo 创建数据库目录失败!
        echo 请检查文件权限或磁盘空间。
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
)

REM 检查数据库是否存在
if not exist app\database\cards.db (
    echo 数据库不存在，正在初始化...
    python init_db.py
    if !errorlevel! neq 0 (
        echo 数据库初始化失败!
        echo 请检查Python环境和依赖是否正确安装。
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo 数据库初始化完成!
)

echo.
echo 正在启动应用...
echo 应用启动后，请在浏览器中访问: http://127.0.0.1:5005
echo.
echo 按 Ctrl+C 可以停止应用
echo ================================================

REM 启动应用，如果出错则捕获
python run.py
set EXIT_CODE=!errorlevel!

if !EXIT_CODE! neq 0 (
    echo.
    echo 应用启动失败，错误代码: !EXIT_CODE!
    echo 请检查Python环境和依赖是否正确安装。
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b !EXIT_CODE!
)

echo.
echo 应用已停止运行
echo.
echo 按任意键退出...
pause >nul 