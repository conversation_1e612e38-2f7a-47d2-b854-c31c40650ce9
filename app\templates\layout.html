<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- 使用Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 自定义Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#22c55e',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#0ea5e9'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .transition-card {
                transition: all 0.3s ease;
            }
            .hover-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
        }
    </style>
    <!-- Markdown 样式支持 -->
    <style>
        /* 基本Markdown样式 */
        .prose h1 { font-size: 2em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h2 { font-size: 1.5em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h3 { font-size: 1.25em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h4 { font-size: 1em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h5 { font-size: 0.875em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        .prose h6 { font-size: 0.85em; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
        
        .prose p { margin-bottom: 1em; }
        .prose a { color: #3b82f6; text-decoration: underline; }
        .prose a:hover { color: #2563eb; }
        
        .prose ul { list-style-type: disc; margin-left: 1.5em; margin-bottom: 1em; }
        .prose ol { list-style-type: decimal; margin-left: 1.5em; margin-bottom: 1em; }
        .prose li { margin-bottom: 0.25em; }
        
        .prose blockquote { 
            border-left: 4px solid #d1d5db; 
            padding-left: 1em; 
            margin-left: 0;
            margin-bottom: 1em;
            color: #6b7280;
        }
        
        .prose pre { 
            background-color: #f3f4f6; 
            padding: 1em; 
            border-radius: 0.25em; 
            overflow-x: auto;
            margin-bottom: 1em;
        }
        
        .prose code { 
            background-color: #f3f4f6; 
            padding: 0.2em 0.4em; 
            border-radius: 0.25em; 
            font-family: monospace;
        }
        
        .prose pre code {
            background-color: transparent;
            padding: 0;
        }
        
        .prose img { 
            max-width: 100%; 
            height: auto;
            margin: 1em 0;
        }
        
        .prose hr {
            border: 0;
            border-top: 1px solid #e5e7eb;
            margin: 2em 0;
        }
        
        .prose table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
        }
        
        .prose th, .prose td {
            border: 1px solid #d1d5db;
            padding: 0.5em;
        }
        
        .prose th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
        
        /* 小预览的样式微调 */
        .markdown-preview h1, .markdown-preview h2, .markdown-preview h3 { 
            margin-top: 0.5em; 
            margin-bottom: 0.25em; 
        }

        /* 分类标签栏样式 */
        .category-tag-bar {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
        }

        .category-tag-item {
            transition: all 0.2s ease;
        }

        .category-tag-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 隐藏滚动条 */
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        /* 卡片标题截断样式 */
        .card-title {
            word-break: break-word;
            overflow-wrap: break-word;
        }

        /* 统一按钮样式系统 */
        .btn-base {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border: none;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn-base:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-base:hover:before {
            left: 100%;
        }

        /* 主要按钮 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            transform: translateY(-1px);
        }

        /* 次要按钮 */
        .btn-secondary {
            background: white;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
            color: #475569;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 危险按钮 */
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
            transform: translateY(-1px);
        }

        /* 成功按钮 */
        .btn-success {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
            transform: translateY(-1px);
        }

        /* 警告按钮 */
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
            transform: translateY(-1px);
        }

        /* 按钮尺寸 */
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            line-height: 1rem;
        }

        .btn-md {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            line-height: 1.5rem;
        }

        /* 轮廓按钮 */
        .btn-outline-primary {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        .btn-outline-primary:hover {
            background: #3b82f6;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-outline-danger {
            background: transparent;
            color: #ef4444;
            border: 2px solid #ef4444;
        }

        .btn-outline-danger:hover {
            background: #ef4444;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        /* 禁用状态 */
        .btn-base:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn-base:disabled:hover:before {
            left: -100% !important;
        }

        /* 分类标签颜色系统 */
        .category-tag {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .category-tag:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .category-tag:hover:before {
            left: 100%;
        }

        .category-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 分类颜色主题 */
        .category-theme-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border-color: #2563eb;
        }

        .category-theme-indigo {
            background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
            color: white;
            border-color: #4f46e5;
        }

        .category-theme-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border-color: #7c3aed;
        }

        .category-theme-pink {
            background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
            color: white;
            border-color: #e11d48;
        }

        .category-theme-red {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-color: #dc2626;
        }

        .category-theme-orange {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            border-color: #ea580c;
        }

        .category-theme-amber {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border-color: #d97706;
        }

        .category-theme-yellow {
            background: linear-gradient(135deg, #eab308 0%, #ca8a04 100%);
            color: white;
            border-color: #ca8a04;
        }

        .category-theme-lime {
            background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
            color: white;
            border-color: #65a30d;
        }

        .category-theme-green {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            border-color: #16a34a;
        }

        .category-theme-emerald {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-color: #059669;
        }

        .category-theme-teal {
            background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
            color: white;
            border-color: #0d9488;
        }

        .category-theme-cyan {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-color: #0891b2;
        }

        .category-theme-sky {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border-color: #0284c7;
        }

        /* 标签颜色主题（较浅的色调） */
        .tag-theme-blue {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-color: #3b82f6;
        }

        .tag-theme-indigo {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            color: #3730a3;
            border-color: #6366f1;
        }

        .tag-theme-purple {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            color: #581c87;
            border-color: #8b5cf6;
        }

        .tag-theme-pink {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
            color: #be185d;
            border-color: #ec4899;
        }

        .tag-theme-red {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #b91c1c;
            border-color: #ef4444;
        }

        .tag-theme-orange {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
            color: #c2410c;
            border-color: #f97316;
        }

        .tag-theme-amber {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #a16207;
            border-color: #f59e0b;
        }

        .tag-theme-yellow {
            background: linear-gradient(135deg, #fef9c3 0%, #fef08a 100%);
            color: #a16207;
            border-color: #eab308;
        }

        .tag-theme-lime {
            background: linear-gradient(135deg, #ecfccb 0%, #d9f99d 100%);
            color: #4d7c0f;
            border-color: #84cc16;
        }

        .tag-theme-green {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #14532d;
            border-color: #22c55e;
        }

        .tag-theme-emerald {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #064e3b;
            border-color: #10b981;
        }

        .tag-theme-teal {
            background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
            color: #134e4a;
            border-color: #14b8a6;
        }

        .tag-theme-cyan {
            background: linear-gradient(135deg, #cffafe 0%, #a5f3fc 100%);
            color: #164e63;
            border-color: #06b6d4;
        }

        .tag-theme-sky {
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
            color: #0c4a6e;
            border-color: #0ea5e9;
        }
    </style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    <!-- 分类标签颜色系统JavaScript -->
    <script>
        // 颜色主题数组
        const categoryThemes = [
            'blue', 'indigo', 'purple', 'pink', 'red', 'orange',
            'amber', 'yellow', 'lime', 'green', 'emerald', 'teal', 'cyan', 'sky'
        ];

        const tagThemes = [
            'blue', 'indigo', 'purple', 'pink', 'red', 'orange',
            'amber', 'yellow', 'lime', 'green', 'emerald', 'teal', 'cyan', 'sky'
        ];

        // 简单哈希函数
        function hashCode(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash);
        }

        // 根据文本获取分类主题
        function getCategoryTheme(text) {
            const index = hashCode(text) % categoryThemes.length;
            return 'category-theme-' + categoryThemes[index];
        }

        // 根据文本获取标签主题
        function getTagTheme(text) {
            const index = hashCode(text) % tagThemes.length;
            return 'tag-theme-' + tagThemes[index];
        }

        // 页面加载完成后应用颜色主题
        document.addEventListener('DOMContentLoaded', function() {
            // 应用分类颜色
            document.querySelectorAll('[data-category]').forEach(function(element) {
                const category = element.getAttribute('data-category');
                const theme = getCategoryTheme(category);
                element.classList.add('category-tag', theme);
            });

            // 应用标签颜色
            document.querySelectorAll('[data-tag]').forEach(function(element) {
                const tag = element.getAttribute('data-tag');
                const theme = getTagTheme(tag);
                element.classList.add('category-tag', theme);
            });
        });
    </script>
</head>
<body class="bg-gray-50 text-gray-800 min-h-screen flex flex-col pt-24 md:pt-32">
    <!-- 固定顶部导航栏 -->
    <header class="bg-gradient-to-r from-blue-800 to-indigo-900 shadow-md fixed top-0 left-0 right-0 z-50">
        <nav class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <a class="text-white text-xl font-bold flex items-center" href="{{ url_for('index') }}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    卡片管理系统
                </a>
                
                <!-- 搜索框 - 桌面版 -->
                <div class="hidden md:block mx-4 flex-grow max-w-md">
                    <form action="{{ url_for('search') }}" method="GET" class="relative" id="searchForm">
                        <input type="text" name="q" id="searchInput" placeholder="搜索卡片内容..." 
                               class="w-full px-4 py-2 rounded-full bg-indigo-700/60 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-primary"
                               autocomplete="off">
                        <div class="absolute right-0 top-0 h-full flex items-center pr-3">
                            <button type="button" id="clearSearch" class="text-indigo-200 hover:text-white mr-2 hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <button type="submit" class="text-indigo-200 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="hidden md:block">
                    <ul class="flex space-x-6">
                        <li>
                            <a class="text-indigo-200 hover:text-white flex items-center transition-colors" href="{{ url_for('index') }}" title="首页">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a class="text-indigo-200 hover:text-white flex items-center transition-colors" href="{{ url_for('new_card') }}" title="新建卡片">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 移动端菜单按钮 -->
                <button id="menu-toggle" class="md:hidden text-indigo-200 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
            
            <!-- 移动端菜单 -->
            <div id="mobile-menu" class="md:hidden hidden mt-3">
                <!-- 搜索框 - 移动版 -->
                <div class="mb-3">
                    <form action="{{ url_for('search') }}" method="GET" class="relative">
                        <input type="text" name="q" placeholder="搜索卡片内容..." 
                               class="w-full px-4 py-2 rounded-full bg-indigo-700/60 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-primary">
                        <button type="submit" class="absolute right-0 top-0 mr-3 mt-2 text-indigo-200 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </form>
                </div>
                <ul class="flex flex-col space-y-3 py-2">
                    <li>
                        <a class="flex items-center text-indigo-200 hover:text-white transition-colors" href="{{ url_for('index') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            首页
                        </a>
                    </li>
                    <li>
                        <a class="flex items-center text-indigo-200 hover:text-white transition-colors" href="{{ url_for('new_card') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            新建卡片
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 分类和标签快速访问栏 -->
    <div class="category-tag-bar fixed top-16 left-0 right-0 z-40 py-1.5 hidden md:block">
        <div class="container mx-auto px-4">
            <div class="flex flex-col space-y-1.5">
                <!-- 分类栏 -->
                {% if all_categories %}
                <div class="flex items-center">
                    <div class="flex items-center mr-3 text-sm font-medium text-gray-600 min-w-max">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        分类:
                    </div>
                    <div class="flex flex-wrap gap-2 overflow-x-auto scrollbar-hide">
                        {% for category in all_categories[:8] %}
                        <a href="{{ url_for('category', category=category) }}"
                           data-category="{{ category }}"
                           class="inline-block text-xs px-3 py-1.5 rounded-full font-medium whitespace-nowrap shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                            {{ category }}
                        </a>
                        {% endfor %}
                        {% if all_categories|length > 8 %}
                        <span class="text-xs text-gray-500 px-2 py-1 whitespace-nowrap">+{{ all_categories|length - 8 }}更多</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 标签栏 -->
                {% if all_tags %}
                <div class="flex items-center">
                    <div class="flex items-center mr-3 text-sm font-medium text-gray-600 min-w-max">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        标签:
                    </div>
                    <div class="flex flex-wrap gap-2 overflow-x-auto scrollbar-hide">
                        {% for tag in all_tags[:10] %}
                        <a href="{{ url_for('tag', tag=tag) }}"
                           data-tag="{{ tag }}"
                           class="inline-block text-xs px-3 py-1.5 rounded-full font-medium whitespace-nowrap shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                            </svg>
                            {{ tag }}
                        </a>
                        {% endfor %}
                        {% if all_tags|length > 10 %}
                        <span class="text-xs text-gray-500 px-2 py-1 whitespace-nowrap">+{{ all_tags|length - 10 }}更多</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 移动端分类标签栏 -->
    <div class="md:hidden fixed top-16 left-0 right-0 z-40 bg-white border-b border-gray-200">
        <div class="px-4 py-1.5">
            <!-- 分类快速访问 -->
            {% if all_categories %}
            <div class="mb-1.5">
                <div class="flex items-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    <span class="text-xs font-medium text-gray-600">分类</span>
                </div>
                <div class="flex gap-1 overflow-x-auto scrollbar-hide pb-1">
                    {% for category in all_categories[:6] %}
                    <a href="{{ url_for('category', category=category) }}"
                       data-category="{{ category }}"
                       class="inline-block text-xs px-2 py-1 rounded-full font-medium whitespace-nowrap">
                        {{ category }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 标签快速访问 -->
            {% if all_tags %}
            <div>
                <div class="flex items-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                    </svg>
                    <span class="text-xs font-medium text-gray-600">标签</span>
                </div>
                <div class="flex gap-1 overflow-x-auto scrollbar-hide pb-1">
                    {% for tag in all_tags[:8] %}
                    <a href="{{ url_for('tag', tag=tag) }}"
                       data-tag="{{ tag }}"
                       class="inline-block text-xs px-2 py-1 rounded-full font-medium whitespace-nowrap">
                        {{ tag }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <main class="container mx-auto px-4 pt-2 pb-6 flex-grow">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% if category == 'success' %}
                        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'danger' or category == 'error' %}
                        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'warning' %}
                        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% elif category == 'info' %}
                        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% else %}
                        <div class="bg-gray-100 border-l-4 border-gray-500 text-gray-700 p-4 mb-4 rounded shadow flash-message" role="alert">
                    {% endif %}
                        <p class="font-medium">{{ message }}</p>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <footer class="bg-gradient-to-r from-blue-800 to-indigo-900 text-gray-300 text-center py-4 mt-auto">
        <p>卡片内容管理系统 &copy; {{ now.year if now else 2025 }}</p>
    </footer>

    <!-- 移动端菜单切换脚本 -->
    <script>
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // 提示消息自动消失功能
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    message.style.transition = 'opacity 0.5s ease';
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.remove();
                    }, 500);
                }, 2000);
            });
        });

        // 搜索功能
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        const clearSearch = document.getElementById('clearSearch');
        let searchTimeout;
        let searchStatus = document.createElement('div');
        searchStatus.className = 'absolute left-0 -bottom-8 text-sm text-blue-600 bg-white px-2 py-1 rounded shadow-sm hidden';
        
        if (searchForm) {
            searchForm.parentNode.style.position = 'relative';
            searchForm.parentNode.appendChild(searchStatus);
        }

        if (searchInput && clearSearch) {
            // 显示/隐藏清除按钮
            searchInput.addEventListener('input', function() {
                clearSearch.classList.toggle('hidden', !this.value);
                
                // 清除之前的延时
                clearTimeout(searchTimeout);
                
                const query = this.value.trim();
                
                // 隐藏状态信息
                searchStatus.classList.add('hidden');
                
                // 如果输入为空，不处理
                if (!query) return;
                
                // 如果字符太少，显示提示但不搜索
                if (query.length < 2) {
                    searchStatus.textContent = '请至少输入2个字符';
                    searchStatus.classList.remove('hidden');
                    return;
                }
                
                // 显示等待提示
                searchStatus.textContent = `将在1秒后搜索: "${query}"`;
                searchStatus.classList.remove('hidden');
                
                // 设置新的延时
                searchTimeout = setTimeout(() => {
                    if (query.length >= 2) {
                        searchForm.submit();
                    }
                }, 1000); // 1000ms 延迟，给用户更多时间考虑和修改
            });

            // 清除搜索内容
            clearSearch.addEventListener('click', function() {
                searchInput.value = '';
                clearSearch.classList.add('hidden');
                searchStatus.classList.add('hidden');
                searchInput.focus();
            });

            // 处理表单提交事件
            searchForm.addEventListener('submit', function(e) {
                const query = searchInput.value.trim();
                
                // 如果搜索词少于2个字符，阻止提交
                if (query.length < 2) {
                    e.preventDefault();
                    searchStatus.textContent = '请至少输入2个字符';
                    searchStatus.classList.remove('hidden');
                    return false;
                }
            });
        }
    </script>
</body>
</html> 