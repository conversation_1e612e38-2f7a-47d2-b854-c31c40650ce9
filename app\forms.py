#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SubmitField, SelectField
from wtforms.validators import DataRequired

class CardForm(FlaskForm):
    title = StringField('标题', validators=[DataRequired()])
    content = TextAreaField('内容', validators=[DataRequired()])
    content_cn = TextAreaField('备注')
    category = StringField('分类')
    tags = StringField('标签', description='多个标签请用逗号分隔，如：重要,工作,会议')
    submit = SubmitField('保存') 