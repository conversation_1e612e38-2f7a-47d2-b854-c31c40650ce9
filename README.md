# 卡片内容管理系统 (Windows版)

一个基于Python Flask和SQLite的简单卡片内容管理系统，可以创建、编辑、删除和分类卡片内容。

## 功能特点

- 创建和管理卡片内容
- 对卡片进行分类
- 查看、编辑和删除卡片
- 按分类浏览卡片
- 支持内容和备注功能

## 技术栈

- Python Flask框架
- SQLite数据库
- SQLAlchemy ORM
- Flask-WTF表单处理
- Bootstrap 5前端框架

## 系统要求

- Windows系统
- Python 3.6或更高版本

## 安装和运行

1. 克隆或下载本仓库

2. 运行环境设置脚本
```
setup.bat
```

3. 如果是更新现有系统，请先运行数据库迁移脚本
```
python migrate_db.py
```

4. 启动应用程序
```
start_app.bat
```

5. 在浏览器中访问 http://127.0.0.1:5005

## 使用说明

- 点击右上角的"新建卡片"按钮创建新卡片
- 填写卡片标题、内容和可选的备注信息
- 点击卡片上的"查看"按钮查看完整内容
- 点击"编辑"按钮修改卡片
- 点击"删除"按钮删除卡片
- 点击分类标签查看同一分类的所有卡片 