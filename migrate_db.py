#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
from app import app

def migrate_database():
    """添加content_cn字段到卡片表"""
    try:
        # 获取数据库路径
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, 'app', 'database', 'cards.db')
        
        # 确认数据库存在
        if not os.path.exists(db_path):
            print(f"错误: 数据库文件不存在: {db_path}")
            return False
        
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已存在content_cn字段
        cursor.execute("PRAGMA table_info(card)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'content_cn' not in column_names:
            print("正在添加content_cn字段...")
            # 添加content_cn字段
            cursor.execute("ALTER TABLE card ADD COLUMN content_cn TEXT")
            conn.commit()
            print("数据库迁移成功: 添加了content_cn字段")
        else:
            print("content_cn字段已存在，无需迁移")
        
        conn.close()
        return True
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    if migrate_database():
        print("数据库迁移完成！")
    else:
        print("数据库迁移失败！") 