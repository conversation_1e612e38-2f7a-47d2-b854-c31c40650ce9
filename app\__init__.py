#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os
import re
from markupsafe import Markup
import markdown
import bleach

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'database', 'cards.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 添加自定义过滤器
@app.template_filter('nl2br')
def nl2br_filter(s):
    if s is None:
        return ""
    return Markup(s.replace('\n', '<br>')) if s else ""

# 添加Markdown过滤器
@app.template_filter('markdown')
def markdown_filter(text):
    if text is None:
        return ""
    # 允许的HTML标签
    allowed_tags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a', 'abbr', 'acronym', 'b', 'blockquote', 
                    'code', 'em', 'i', 'li', 'ol', 'ul', 'strong', 'span', 'table', 'tbody', 'thead', 
                    'tr', 'th', 'td', 'pre', 'br', 'hr', 'img']
    allowed_attrs = {
        '*': ['class', 'style'],
        'a': ['href', 'title', 'target'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
    }
    
    # 转换Markdown为HTML并净化
    html = markdown.markdown(text, extensions=['extra', 'codehilite', 'tables'])
    clean_html = bleach.clean(html, tags=allowed_tags, attributes=allowed_attrs, strip=True)
    
    return Markup(clean_html)

db = SQLAlchemy(app)

from app import routes, models