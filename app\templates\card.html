{% extends "layout.html" %}
{% block content %}
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">{{ card.title }}</h1>
            <div class="flex flex-wrap items-center text-gray-600 gap-2">
                <p class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {{ card.created_date.strftime('%Y-%m-%d') }}
                </p>
                {% if card.category %}
                <span class="text-gray-400 mx-1">•</span>
                <a href="{{ url_for('category', category=card.category) }}" class="flex items-center text-blue-600 hover:text-blue-800 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    {{ card.category }}
                </a>
                {% endif %}
            </div>
        </div>
        <div class="mt-4 md:mt-0 flex flex-col md:flex-row gap-3">
            <a href="{{ url_for('index') }}" class="btn-base btn-secondary btn-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回首页
            </a>
            <a href="{{ url_for('update_card', card_id=card.id) }}" class="btn-base btn-primary btn-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑
            </a>
            <button onclick="showDeleteModal()" class="btn-base btn-danger btn-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                删除
            </button>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-6">
        <!-- 卡片顶部彩色条纹 -->
        <div class="h-2 bg-gradient-to-r from-blue-800 to-indigo-900"></div>
        
        <div class="p-6">
            {% if card.tags %}
                <div class="mb-4 flex flex-wrap gap-2">
                    {% for tag in card.tags.split(',') %}
                        {% if tag.strip() %}
                            <a href="{{ url_for('tag', tag=tag.strip()) }}" 
                               class="inline-block bg-blue-600 text-white text-xs px-3 py-1 rounded-full hover:bg-blue-700 transition-colors">
                                <span class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                    </svg>
                                    {{ tag.strip() }}
                                </span>
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="prose prose-lg max-w-none">
                {{ card.content|markdown }}
            </div>
            
            {% if card.content_cn %}
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-xl font-semibold text-gray-800 mb-3">备注</h3>
                <div class="prose prose-lg max-w-none">
                    {{ card.content_cn|markdown }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 删除确认模态框 (隐藏状态) -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
            <div class="p-4 border-b flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
            </div>
            <div class="p-4">
                <p>你确定要删除 "{{ card.title }}" 这张卡片吗?</p>
                <p class="text-gray-500 text-sm mt-2">此操作不可逆，请谨慎操作。</p>
            </div>
            <div class="flex justify-end p-4 border-t space-x-3">
                <button onclick="hideDeleteModal()" class="btn-base btn-secondary btn-md">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    取消
                </button>
                <form action="{{ url_for('delete_card', card_id=card.id) }}" method="POST">
                    <button type="submit" class="btn-base btn-danger btn-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        确认删除
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框的脚本 -->
    <script>
        function showDeleteModal() {
            document.getElementById('deleteModal').classList.remove('hidden');
        }
        
        function hideDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }
    </script>
{% endblock content %} 