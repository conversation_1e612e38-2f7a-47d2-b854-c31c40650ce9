#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import db
from datetime import datetime

class Card(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    content_cn = db.Column(db.Text)
    category = db.Column(db.String(50))
    tags = db.Column(db.String(200))
    created_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_pinned = db.Column(db.Boolean, default=False)
    
    def __repr__(self):
        return f'<Card {self.title}>' 