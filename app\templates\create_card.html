{% extends "layout.html" %}
{% block content %}
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <form method="POST" action="">
            {{ form.hidden_tag() }}
            <div class="mb-6">
                <h2 class="text-2xl font-bold mb-4 pb-2 border-b border-gray-200">{{ legend }}</h2>
                
                <div class="mb-5">
                    {{ form.title.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.title.errors %}
                        {{ form.title(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.title(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                </div>
                
                <div class="mb-5">
                    {{ form.content.label(class="block text-gray-700 font-medium mb-2") }}
                    <div class="flex justify-between items-center mb-2">
                        <label class="text-gray-600 text-sm">支持Markdown格式</label>
                        <button type="button" id="preview-toggle" class="text-blue-600 hover:text-blue-800 text-sm transition">预览</button>
                    </div>
                    {% if form.content.errors %}
                        {{ form.content(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200", rows=10, id="md-editor") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.content.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.content(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500", rows=10, id="md-editor") }}
                    {% endif %}
                    <div id="md-preview" class="hidden w-full border border-gray-300 rounded px-3 py-2 mt-2 prose prose-sm h-64 overflow-auto"></div>
                </div>
                
                <div class="mb-5">
                    {{ form.content_cn.label(class="block text-gray-700 font-medium mb-2") }}
                    <div class="flex justify-between items-center mb-2">
                        <label class="text-gray-600 text-sm">备注 (可选)</label>
                        <button type="button" id="preview-toggle-cn" class="text-blue-600 hover:text-blue-800 text-sm transition">预览</button>
                    </div>
                    {% if form.content_cn.errors %}
                        {{ form.content_cn(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200", rows=10, id="md-editor-cn") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.content_cn.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.content_cn(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500", rows=10, id="md-editor-cn") }}
                    {% endif %}
                    <div id="md-preview-cn" class="hidden w-full border border-gray-300 rounded px-3 py-2 mt-2 prose prose-sm h-64 overflow-auto"></div>
                </div>
                
                <div class="mb-5">
                    {{ form.category.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.category.errors %}
                        {{ form.category(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.category.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.category(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                </div>
                
                <div class="mb-5">
                    {{ form.tags.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.tags.errors %}
                        {{ form.tags(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.tags.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.tags(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                    <p class="text-gray-500 text-sm mt-1">{{ form.tags.description }}</p>
                    <div class="flex flex-wrap gap-2 mt-2 tags-preview"></div>
                </div>
            </div>
            
            <div class="flex space-x-3">
                {{ form.submit(class="bg-primary hover:bg-blue-600 text-white font-medium py-2 px-4 rounded shadow transition-colors") }}
                <a href="{{ url_for('index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded shadow transition-colors">取消</a>
            </div>
        </form>
    </div>
    
    <script>
        // 标签预览功能
        const tagsInput = document.querySelector('input[name="tags"]');
        const tagsPreview = document.querySelector('.tags-preview');
        
        function updateTagsPreview() {
            if (!tagsInput || !tagsPreview) return;
            
            const tags = tagsInput.value.split(',').filter(tag => tag.trim());
            tagsPreview.innerHTML = '';
            
            tags.forEach(tag => {
                if (tag.trim()) {
                    const tagElement = document.createElement('span');
                    tagElement.className = 'inline-block bg-info/80 text-white text-xs px-3 py-1 rounded-full';
                    tagElement.textContent = tag.trim();
                    tagsPreview.appendChild(tagElement);
                }
            });
        }
        
        if (tagsInput) {
            tagsInput.addEventListener('input', updateTagsPreview);
            // 初始化
            updateTagsPreview();
        }
        
        // Markdown预览功能
        const mdEditor = document.getElementById('md-editor');
        const mdPreview = document.getElementById('md-preview');
        const previewToggle = document.getElementById('preview-toggle');
        
        if (previewToggle && mdEditor && mdPreview) {
            let previewMode = false;
            
            previewToggle.addEventListener('click', function() {
                previewMode = !previewMode;
                if (previewMode) {
                    // 切换到预览模式
                    mdEditor.classList.add('hidden');
                    mdPreview.classList.remove('hidden');
                    previewToggle.textContent = '编辑';
                    
                    // 获取编辑器内容并转换为Markdown HTML
                    fetch('/api/markdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ text: mdEditor.value })
                    })
                    .then(response => response.json())
                    .then(data => {
                        mdPreview.innerHTML = data.html;
                    });
                } else {
                    // 切换回编辑模式
                    mdEditor.classList.remove('hidden');
                    mdPreview.classList.add('hidden');
                    previewToggle.textContent = '预览';
                }
            });
        }
        
        // 中文内容的Markdown预览功能
        const mdEditorCn = document.getElementById('md-editor-cn');
        const mdPreviewCn = document.getElementById('md-preview-cn');
        const previewToggleCn = document.getElementById('preview-toggle-cn');
        
        if (previewToggleCn && mdEditorCn && mdPreviewCn) {
            let previewModeCn = false;
            
            previewToggleCn.addEventListener('click', function() {
                previewModeCn = !previewModeCn;
                if (previewModeCn) {
                    // 切换到预览模式
                    mdEditorCn.classList.add('hidden');
                    mdPreviewCn.classList.remove('hidden');
                    previewToggleCn.textContent = '编辑';
                    
                    // 获取编辑器内容并转换为Markdown HTML
                    fetch('/api/markdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ text: mdEditorCn.value })
                    })
                    .then(response => response.json())
                    .then(data => {
                        mdPreviewCn.innerHTML = data.html;
                    });
                } else {
                    // 切换回编辑模式
                    mdEditorCn.classList.remove('hidden');
                    mdPreviewCn.classList.add('hidden');
                    previewToggleCn.textContent = '预览';
                }
            });
        }
    </script>
{% endblock content %} 