#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import db
from app.models import Card

# 创建数据库表
db.create_all()

# 添加一些示例卡片
card1 = Card(
    title="欢迎使用卡片管理系统",
    content="这是一个简单的基于Flask和SQLite的卡片内容管理系统。你可以创建、编辑、删除和分类不同的卡片。",
    category="系统"
)

card2 = Card(
    title="如何使用",
    content="1. 点击右上角的'新建卡片'创建新卡片\n2. 点击卡片上的'查看'按钮查看完整内容\n3. 使用'编辑'按钮修改卡片\n4. 使用'删除'按钮删除不需要的卡片\n5. 点击分类标签查看同一分类的卡片",
    category="使用指南"
)

card3 = Card(
    title="开发技术",
    content="本系统使用以下技术开发：\n- Python Flask 框架\n- SQLite 数据库\n- SQLAlchemy ORM\n- Bootstrap 5 前端框架\n- WTForms 表单处理",
    category="技术"
)

# 添加到会话
db.session.add(card1)
db.session.add(card2)
db.session.add(card3)

# 提交到数据库
db.session.commit()

print("数据库初始化完成！") 