#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import render_template, redirect, url_for, flash, request, jsonify
from app import app, db
from app.models import Card
from app.forms import CardForm
from sqlalchemy import or_, func

def get_all_categories():
    """获取所有分类"""
    categories = db.session.query(Card.category).filter(Card.category.isnot(None), Card.category != '').distinct().all()
    return [cat[0] for cat in categories if cat[0]]

def get_all_tags():
    """获取所有标签"""
    all_tags = set()
    cards_with_tags = Card.query.filter(Card.tags.isnot(None), Card.tags != '').all()
    for card in cards_with_tags:
        if card.tags:
            tags = [tag.strip() for tag in card.tags.split(',') if tag.strip()]
            all_tags.update(tags)
    return sorted(list(all_tags))

@app.context_processor
def inject_categories_and_tags():
    """向所有模板注入分类和标签数据"""
    return {
        'all_categories': get_all_categories(),
        'all_tags': get_all_tags()
    }

@app.route('/')
def index():
    # 先获取置顶的卡片，再获取其他卡片
    pinned_cards = Card.query.filter_by(is_pinned=True).order_by(Card.created_date.desc()).all()
    unpinned_cards = Card.query.filter_by(is_pinned=False).order_by(Card.created_date.desc()).all()
    cards = pinned_cards + unpinned_cards
    return render_template('index.html', title='首页', cards=cards)

@app.route('/search')
def search():
    query = request.args.get('q', '')
    if not query:
        return redirect(url_for('index'))
    
    # 使用LIKE进行模糊搜索，在标题和内容中查找
    search_term = f"%{query}%"
    cards = Card.query.filter(
        or_(
            Card.title.like(search_term),
            Card.content.like(search_term),
            Card.content_cn.like(search_term),
            Card.category.like(search_term),
            Card.tags.like(search_term)
        )
    ).order_by(Card.updated_date.desc()).all()
    
    return render_template(
        'search_results.html', 
        title=f'搜索结果: {query}', 
        query=query, 
        cards=cards
    )

@app.route('/card/new', methods=['GET', 'POST'])
def new_card():
    form = CardForm()
    if form.validate_on_submit():
        card = Card(title=form.title.data, content=form.content.data, 
                   content_cn=form.content_cn.data, category=form.category.data, 
                   tags=form.tags.data)
        db.session.add(card)
        db.session.commit()
        flash('卡片已创建!', 'success')
        return redirect(url_for('index'))
    return render_template('create_card.html', title='新建卡片', form=form, legend='新建卡片')

@app.route('/card/<int:card_id>')
def card(card_id):
    card = Card.query.get_or_404(card_id)
    return render_template('card.html', title=card.title, card=card)

@app.route('/card/<int:card_id>/update', methods=['GET', 'POST'])
def update_card(card_id):
    card = Card.query.get_or_404(card_id)
    form = CardForm()
    if form.validate_on_submit():
        card.title = form.title.data
        card.content = form.content.data
        card.content_cn = form.content_cn.data
        card.category = form.category.data
        card.tags = form.tags.data
        db.session.commit()
        flash('卡片已更新!', 'success')
        return redirect(url_for('card', card_id=card.id))
    elif request.method == 'GET':
        form.title.data = card.title
        form.content.data = card.content
        form.content_cn.data = card.content_cn
        form.category.data = card.category
        form.tags.data = card.tags
    return render_template('create_card.html', title='更新卡片', form=form, legend='更新卡片')

@app.route('/card/<int:card_id>/delete', methods=['POST'])
def delete_card(card_id):
    card = Card.query.get_or_404(card_id)
    db.session.delete(card)
    db.session.commit()
    flash('卡片已删除!', 'success')
    return redirect(url_for('index'))

@app.route('/category/<string:category>')
def category(category):
    cards = Card.query.filter_by(category=category).order_by(Card.updated_date.desc()).all()
    return render_template('category.html', title=f'分类: {category}', cards=cards, category=category)

@app.route('/tag/<string:tag>')
def tag(tag):
    # 使用LIKE进行标签搜索
    search_term = f"%{tag}%"
    cards = Card.query.filter(Card.tags.like(search_term)).order_by(Card.updated_date.desc()).all()
    # 过滤出真正包含此标签的卡片
    filtered_cards = []
    for card in cards:
        if card.tags:
            card_tags = [t.strip() for t in card.tags.split(',')]
            if tag in card_tags:
                filtered_cards.append(card)
    
    return render_template('category.html', title=f'标签: {tag}', cards=filtered_cards, category=f'标签: {tag}')

@app.route('/card/<int:card_id>/toggle_pin', methods=['POST'])
def toggle_pin(card_id):
    card = Card.query.get_or_404(card_id)
    card.is_pinned = not card.is_pinned
    db.session.commit()
    flash('卡片已更新!', 'success')
    return redirect(url_for('index'))

@app.route('/api/markdown', methods=['POST'])
def render_markdown():
    """将Markdown文本转换为HTML的API端点"""
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({'error': '缺少必要的text字段'}), 400
    
    html = app.jinja_env.filters['markdown'](data['text'])
    return jsonify({'html': html}) 