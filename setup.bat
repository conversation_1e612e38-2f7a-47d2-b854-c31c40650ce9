@echo off
chcp 65001 > nul
title 卡片管理系统 - 环境设置

REM 一定要先启用延迟环境变量扩展
setlocal enabledelayedexpansion

echo =========================================================
echo             卡片内容管理系统 - 环境设置
echo =========================================================
echo.

REM 检查Python是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Python!
    echo 请安装Python 3.6或更高版本后再运行此脚本。
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo 检测Python版本...
python --version
echo.

REM 安装virtualenv
echo 正在检查virtualenv...
pip show virtualenv >nul 2>&1
if %errorlevel% neq 0 (
    echo virtualenv未安装，正在安装...
    echo 使用清华镜像源安装virtualenv...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple virtualenv
    if %errorlevel% neq 0 (
        echo 安装virtualenv失败!
        echo 请检查网络连接或使用管理员权限运行此脚本。
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
)

REM 检查虚拟环境是否已存在
set venv_dir=venv
if exist %venv_dir% (
    echo.
    echo 检测到虚拟环境已存在。
    echo 由于依赖关系问题，建议重新创建环境。
    choice /c YN /m "是否重新创建？(Y=是, N=否)"
    if !errorlevel! equ 1 (
        echo 正在删除旧的虚拟环境...
        call :cleanup_venv
    ) else (
        echo 将使用现有虚拟环境...
    )
)

REM 创建虚拟环境
if not exist %venv_dir% (
    echo.
    echo 正在创建虚拟环境...
    python -m virtualenv %venv_dir%
    if !errorlevel! neq 0 (
        echo 创建虚拟环境失败!
        echo 请检查Python版本或磁盘空间。
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo 虚拟环境创建成功!
)

REM 激活虚拟环境并安装依赖
echo.
echo 正在激活虚拟环境...
call %venv_dir%\Scripts\activate.bat

echo.
echo 正在配置pip使用清华镜像源，以加快下载速度...
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn

echo.
echo 正在安装依赖...
echo 首先清理pip缓存，确保安装正确版本...
pip cache purge
echo.

echo 安装依赖包，请稍候...
echo.

echo 正在使用requirements.txt安装所有依赖...
pip install -r requirements.txt
if !errorlevel! neq 0 goto :install_error

echo.
echo 所有依赖安装完成!

echo.
echo 验证依赖包版本:
pip list | findstr /C:"Flask" /C:"SQLAlchemy" /C:"Werkzeug" /C:"markdown" /C:"bleach"
echo.

REM 检查数据库目录
if not exist app\database (
    echo.
    echo 创建数据库目录...
    mkdir app\database
    if !errorlevel! neq 0 (
        echo 创建数据库目录失败!
        echo 请检查文件权限或磁盘空间。
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
)

echo.
echo =========================================================
echo 环境设置完成!
echo.
echo 你可以运行 start_app.bat 来启动项目
echo =========================================================
echo.
echo 按任意键退出...
pause >nul
exit /b 0

:install_error
echo.
echo 安装依赖失败!
echo 请检查网络连接，或尝试以管理员身份运行此脚本。
echo.
echo 按任意键退出...
pause >nul
exit /b 1

:cleanup_venv
REM 尝试多种方法删除旧虚拟环境
echo 尝试方法1: 使用rmdir命令
rmdir /s /q %venv_dir%
if not exist %venv_dir% (
    echo 虚拟环境已成功删除。
    exit /b 0
)

echo 方法1失败，尝试方法2: 使用del命令
del /f /s /q %venv_dir%\* >nul 2>&1
rmdir /s /q %venv_dir%
if not exist %venv_dir% (
    echo 虚拟环境已成功删除。
    exit /b 0
)

echo 自动删除失败，请手动删除venv目录后重试。
echo 可以关闭此窗口，删除venv文件夹后再次运行setup.bat
echo.
echo 按任意键退出...
pause >nul
exit /b 1 