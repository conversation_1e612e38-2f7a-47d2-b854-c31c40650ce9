{% extends "layout.html" %}
{% block content %}
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <form method="POST" action="">
            {{ form.hidden_tag() }}
            <div class="mb-6">
                <h2 class="text-2xl font-bold mb-4 pb-2 border-b border-gray-200">{{ legend }}</h2>
                
                <div class="mb-5">
                    {{ form.title.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.title.errors %}
                        {{ form.title(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.title(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                </div>
                
                <div class="mb-5">
                    {{ form.content.label(class="block text-gray-700 font-medium mb-2") }}
                    <div class="flex justify-between items-center mb-2">
                        <label class="text-gray-600 text-sm">支持Markdown格式</label>
                        <button type="button" id="preview-toggle" class="btn-base btn-outline-primary btn-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            预览
                        </button>
                    </div>
                    {% if form.content.errors %}
                        {{ form.content(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200 resize-none", rows=15, id="md-editor") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.content.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.content(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500 resize-none", rows=15, id="md-editor") }}
                    {% endif %}
                    <div id="md-preview" class="hidden w-full border border-gray-300 rounded px-3 py-2 mt-2 prose prose-sm h-64 overflow-auto"></div>
                </div>
                
                <div class="mb-5">
                    {{ form.content_cn.label(class="block text-gray-700 font-medium mb-2") }}
                    <label class="text-gray-600 text-sm mb-2 block">备注 (可选)</label>
                    {% if form.content_cn.errors %}
                        {{ form.content_cn(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.content_cn.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.content_cn(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                </div>
                
                <div class="mb-5">
                    {{ form.category.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.category.errors %}
                        {{ form.category(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.category.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.category(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                </div>
                
                <div class="mb-5">
                    {{ form.tags.label(class="block text-gray-700 font-medium mb-2") }}
                    {% if form.tags.errors %}
                        {{ form.tags(class="w-full border border-red-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-200") }}
                        <div class="text-red-500 text-sm mt-1">
                            {% for error in form.tags.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.tags(class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-500") }}
                    {% endif %}
                    <p class="text-gray-500 text-sm mt-1">{{ form.tags.description }}</p>
                    <div class="flex flex-wrap gap-2 mt-2 tags-preview"></div>
                </div>
            </div>
            
            <div class="flex space-x-4">
                {{ form.submit(class="btn-base btn-primary btn-lg") }}
                <a href="{{ url_for('index') }}" class="btn-base btn-secondary btn-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    取消
                </a>
            </div>
        </form>
    </div>
    
    <script>
        // 自动调整textarea高度
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        // 初始化内容输入框的自动高度调整
        const mdEditor = document.getElementById('md-editor');
        if (mdEditor) {
            // 初始化高度
            autoResizeTextarea(mdEditor);

            // 监听输入事件
            mdEditor.addEventListener('input', function() {
                autoResizeTextarea(this);
            });

            // 监听粘贴事件
            mdEditor.addEventListener('paste', function() {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 10);
            });
        }

        // 标签预览功能
        const tagsInput = document.querySelector('input[name="tags"]');
        const tagsPreview = document.querySelector('.tags-preview');
        
        function updateTagsPreview() {
            if (!tagsInput || !tagsPreview) return;
            
            const tags = tagsInput.value.split(',').filter(tag => tag.trim());
            tagsPreview.innerHTML = '';
            
            tags.forEach(tag => {
                if (tag.trim()) {
                    const tagElement = document.createElement('span');
                    tagElement.className = 'inline-block bg-info/80 text-white text-xs px-3 py-1 rounded-full';
                    tagElement.textContent = tag.trim();
                    tagsPreview.appendChild(tagElement);
                }
            });
        }
        
        if (tagsInput) {
            tagsInput.addEventListener('input', updateTagsPreview);
            // 初始化
            updateTagsPreview();
        }
        
        // Markdown预览功能
        const mdPreview = document.getElementById('md-preview');
        const previewToggle = document.getElementById('preview-toggle');
        
        if (previewToggle && mdEditor && mdPreview) {
            let previewMode = false;
            
            previewToggle.addEventListener('click', function() {
                previewMode = !previewMode;
                if (previewMode) {
                    // 切换到预览模式
                    mdEditor.classList.add('hidden');
                    mdPreview.classList.remove('hidden');
                    previewToggle.textContent = '编辑';
                    
                    // 获取编辑器内容并转换为Markdown HTML
                    fetch('/api/markdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ text: mdEditor.value })
                    })
                    .then(response => response.json())
                    .then(data => {
                        mdPreview.innerHTML = data.html;
                    });
                } else {
                    // 切换回编辑模式
                    mdEditor.classList.remove('hidden');
                    mdPreview.classList.add('hidden');
                    previewToggle.textContent = '预览';
                }
            });
        }
        

    </script>
{% endblock content %} 