{% extends "layout.html" %}
{% block content %}
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">分类: {{ category }}</h1>
            <p class="text-gray-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                共 {{ cards|length }} 张卡片
            </p>
        </div>
        <a href="{{ url_for('index') }}" class="btn-base btn-outline-primary btn-md mt-3 md:mt-0">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回首页
        </a>
    </div>

    {% if cards %}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
            {% for card in cards %}
                <div class="bg-white rounded-xl shadow-md overflow-hidden transition-card hover-card border border-gray-100" data-card-id="{{ card.id }}">
                    <!-- 卡片顶部彩色条纹 -->
                    <div class="h-1.5 bg-gradient-to-r from-blue-800 to-indigo-900"></div>
                    
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-2 gap-2">
                            <div class="flex items-center flex-1 min-w-0">
                                <h2 class="text-lg font-semibold text-gray-800 line-clamp-1 card-title truncate flex-1">{{ card.title }}</h2>
                                {% if card.is_pinned %}
                                    <span class="ml-2 text-xs px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded-md font-medium flex items-center flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                        </svg>
                                        置顶
                                    </span>
                                {% endif %}
                            </div>
                            <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-md font-medium flex items-center flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                {{ card.created_date.strftime('%m-%d') }}
                            </span>
                        </div>
                        <p class="text-gray-600 mb-3 h-16 overflow-hidden line-clamp-3 text-sm card-content markdown-preview">{{ card.content|truncate(90) }}</p>
                        
                        <div class="flex flex-wrap gap-1.5 mb-2 items-center">
                            {% if card.category %}
                                <a href="{{ url_for('category', category=card.category) }}"
                                   data-category="{{ card.category }}"
                                   class="inline-block text-xs px-2 py-1 rounded-full font-medium">
                                    <span class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                        </svg>
                                        {{ card.category }}
                                    </span>
                                </a>
                            {% endif %}

                            {% if card.tags %}
                                {% for tag in card.tags.split(',') %}
                                    {% if tag.strip() %}
                                        <a href="{{ url_for('tag', tag=tag.strip()) }}"
                                           data-tag="{{ tag.strip() }}"
                                           class="inline-block text-xs px-2 py-1 rounded-full font-medium">
                                            <span class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                                </svg>
                                                {{ tag.strip() }}
                                            </span>
                                        </a>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 px-4 py-2 border-t border-gray-100">
                        <!-- 使用单个flex容器包含所有按钮 -->
                        <div class="flex w-full">
                            <!-- 置顶按钮 -->
                            <form action="{{ url_for('toggle_pin', card_id=card.id) }}" method="POST" class="inline-block">
                                <button type="submit"
                                        class="h-7 text-{{ 'yellow' if card.is_pinned else 'gray' }}-600 border border-{{ 'yellow' if card.is_pinned else 'gray' }}-600 hover:bg-{{ 'yellow' if card.is_pinned else 'gray' }}-600 hover:text-white rounded-l px-2 py-0.5 text-xs transition-colors flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                    </svg>
                                    {{ '取消' if card.is_pinned else '置顶' }}
                                </button>
                            </form>

                            <!-- 复制按钮 -->
                            <button id="copy-btn-{{ card.id }}"
                                    class="h-7 text-green-600 border-t border-b border-r border-green-600 hover:bg-green-600 hover:text-white px-2 py-0.5 text-xs transition-colors flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                                复制
                            </button>

                            <!-- 查看按钮 -->
                            <a href="{{ url_for('card', card_id=card.id) }}"
                               class="h-7 text-blue-600 border-t border-b border-r border-blue-600 hover:bg-blue-600 hover:text-white px-2 py-0.5 text-xs transition-colors flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                查看
                            </a>

                            <!-- 编辑按钮 -->
                            <a href="{{ url_for('update_card', card_id=card.id) }}"
                               class="h-7 text-indigo-600 border-t border-b border-r border-indigo-600 hover:bg-indigo-600 hover:text-white px-2 py-0.5 text-xs transition-colors flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                编辑
                            </a>

                            <!-- 删除按钮 -->
                            <button onclick="showDeleteModal('{{ card.id }}')"
                                    class="h-7 text-red-600 border-t border-b border-r border-red-600 hover:bg-red-600 hover:text-white rounded-r px-2 py-0.5 text-xs transition-colors flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                删除
                            </button>
                        </div>

                        <script>
                            document.getElementById("copy-btn-{{ card.id }}").addEventListener("click", function() {
                                const card = document.querySelector(`[data-card-id="{{ card.id }}"]`);
                                const content = card.querySelector('.card-content').textContent;
                                
                                // 创建临时文本区域
                                const textarea = document.createElement('textarea');
                                textarea.value = content;
                                document.body.appendChild(textarea);
                                
                                // 选择文本
                                textarea.select();
                                textarea.setSelectionRange(0, 99999);
                                
                                // 复制
                                document.execCommand('copy');
                                
                                // 移除临时文本区
                                document.body.removeChild(textarea);
                                
                                // 显示成功
                                const button = this;
                                const originalHTML = button.innerHTML;
                                button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>已复制`;
                                button.classList.add('bg-green-100');
                                
                                setTimeout(() => {
                                    button.innerHTML = originalHTML;
                                    button.classList.remove('bg-green-100');
                                }, 2000);
                            });
                        </script>
                    </div>
                </div>

                <!-- 删除确认模态框 (隐藏状态) -->
                <div id="deleteModal-{{ card.id }}" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
                    <div class="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
                        <div class="p-4 border-b flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
                        </div>
                        <div class="p-4">
                            <p>你确定要删除 "{{ card.title }}" 这张卡片吗?</p>
                            <p class="text-gray-500 text-sm mt-2">此操作不可逆，请谨慎操作。</p>
                        </div>
                        <div class="flex justify-end p-4 border-t space-x-3">
                            <button onclick="hideDeleteModal('{{ card.id }}')"
                                    class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors">
                                取消
                            </button>
                            <form action="{{ url_for('delete_card', card_id=card.id) }}" method="POST">
                                <button type="submit"
                                        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                    删除
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 rounded-lg shadow flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            该分类下没有卡片。
        </div>
    {% endif %}

    <!-- 删除确认模态框的脚本 -->
    <script>
        function showDeleteModal(id) {
            document.getElementById('deleteModal-' + id).classList.remove('hidden');
        }
        
        function hideDeleteModal(id) {
            document.getElementById('deleteModal-' + id).classList.add('hidden');
        }
    </script>
{% endblock content %} 